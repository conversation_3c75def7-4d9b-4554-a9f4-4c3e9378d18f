{"name": "text-input-v3", "version": "1.0.0", "description": "Enhanced text input control for Power Apps", "scripts": {"build": "pcf-scripts build", "clean": "pcf-scripts clean", "rebuild": "pcf-scripts rebuild", "start": "pcf-scripts start", "refreshTypes": "pcf-scripts refreshTypes"}, "dependencies": {"@types/node": "^16.x", "@types/powerapps-component-framework": "^1.3.0", "react": "^17.0.2", "react-dom": "^17.0.2"}, "devDependencies": {"@microsoft/eslint-plugin-power-apps": "^0.3.7", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "eslint-plugin-promise": "^7.2.1", "pcf-scripts": "^1.13.6", "pcf-start": "^1.13.6", "typescript": "^4.3.5"}}