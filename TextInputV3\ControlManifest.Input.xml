<?xml version="1.0" encoding="utf-8"?>
<manifest>
  <control namespace="gjpepsi" constructor="TextInputV3" version="1.1.1" display-name-key="TextInputV3" description-key="Text input control" control-type="standard">
    <property name="text" display-name-key="Text" description-key="Text value" of-type="SingleLine.Text" usage="bound" required="false"/>
    <property name="triggerOnFocusOut" display-name-key="Trigger On Focus Out" description-key="Trigger output when focus leaves the control" of-type="TwoOptions" usage="input" required="false" default-value="true"/>
    <property name="triggerWhileTyping" display-name-key="Trigger While Typing" description-key="Trigger output while typing" of-type="TwoOptions" usage="input" required="false" default-value="false"/>
    <property name="triggerDelayed" display-name-key="Trigger Delayed" description-key="Trigger output with delay" of-type="TwoOptions" usage="input" required="false" default-value="false"/>
    <property name="delayMs" display-name-key="Delay (ms)" description-key="Delay in milliseconds for delayed trigger" of-type="Whole.None" usage="input" required="false" default-value="500"/>
    <property name="useNumericKeyboard" display-name-key="Numeric Keyboard" description-key="Use numeric virtual keyboard" of-type="TwoOptions" usage="input" required="false" default-value="false"/>
    <property name="hintText" display-name-key="Hint Text" description-key="Placeholder text shown when input is empty" of-type="SingleLine.Text" usage="input" required="false"/>
    <property name="borderColor" display-name-key="Border Color" description-key="Color of the input border" of-type="SingleLine.Text" usage="input" required="false" default-value="#c8c8c8"/>
    <property name="borderThickness" display-name-key="Border Thickness" description-key="Thickness of the input border in pixels" of-type="Whole.None" usage="input" required="false" default-value="1"/>
    <property name="height" display-name-key="Height" description-key="Height of the input control in pixels" of-type="Whole.None" usage="input" required="false" default-value="32"/>
    <property name="textAlignCenter" display-name-key="Center Align" description-key="Center align the text" of-type="TwoOptions" usage="input" required="false" default-value="false"/>
    <property name="textAlignRight" display-name-key="Right Align" description-key="Right align the text" of-type="TwoOptions" usage="input" required="false" default-value="false"/>
    <property name="fontFamily" display-name-key="Font Family" description-key="Font family for the input text" of-type="SingleLine.Text" usage="input" required="false" default-value="Segoe UI, Arial, sans-serif"/>
    <property name="fontBold" display-name-key="Bold Font" description-key="Make the font bold" of-type="TwoOptions" usage="input" required="false" default-value="false"/>
    <property name="fontSize" display-name-key="Font Size" description-key="Font size in pixels" of-type="Whole.None" usage="input" required="false" default-value="14"/>
    <property name="maxLength" display-name-key="Max Length" description-key="Maximum number of characters allowed" of-type="Whole.None" usage="input" required="false"/>
    <property name="defaultValue" display-name-key="Default Value" description-key="Default text value for the input" of-type="SingleLine.Text" usage="input" required="false"/>
    <resources>
      <code path="index.ts" order="1"/>
    </resources>
    <feature-usage>
      <uses-feature name="PropertyBinding" required="true"/>
    </feature-usage>
  </control>
</manifest>