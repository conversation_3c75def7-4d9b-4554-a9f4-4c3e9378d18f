import { IInputs, IOutputs } from "./generated/ManifestTypes";

export class TextInputV3 implements ComponentFramework.StandardControl<IInputs, IOutputs> {
    private _container: HTMLDivElement;
    private _inputElement: HTMLInputElement;
    private _notifyOutputChanged: () => void;
    private _value: string = "";
    private _triggerOnFocusOut: boolean = true;
    private _triggerWhileTyping: boolean = false;
    private _triggerDelayed: boolean = false;
    private _delayTimer: number | undefined;
    private _context: ComponentFramework.Context<IInputs>;

    constructor() { }

    public init(
        context: ComponentFramework.Context<IInputs>,
        notifyOutputChanged: () => void,
        state: ComponentFramework.Dictionary,
        container: HTMLDivElement
    ): void {
        this._context = context;
        this._container = container;
        this._notifyOutputChanged = notifyOutputChanged;
        this._triggerOnFocusOut = context.parameters.triggerOnFocusOut.raw ?? true;
        this._triggerWhileTyping = context.parameters.triggerWhileTyping.raw ?? false;
        this._triggerDelayed = context.parameters.triggerDelayed.raw ?? false;

        // Create input element
        this._inputElement = document.createElement("input");
        this._inputElement.classList.add("text-input-v3");

        // Initialize element
        this.updateInputElement(context);

        // Add event listeners
        this._inputElement.addEventListener("input", this.handleInput.bind(this));
        this._inputElement.addEventListener("blur", this.handleBlur.bind(this));

        // Add to container
        container.appendChild(this._inputElement);
    }

    private updateInputElement(context: ComponentFramework.Context<IInputs>): void {
        // Set value from bound property, fallback to default value, then empty string
        const boundValue = context.parameters.text.raw;
        const defaultValue = context.parameters.defaultValue.raw;
        this._inputElement.value = boundValue || defaultValue || "";

        // Basic input setup
        this._inputElement.type = "text";
        this._inputElement.disabled = context.mode.isControlDisabled;

        // Set hint text (placeholder)
        this._inputElement.placeholder = context.parameters.hintText.raw || "";

        // Virtual keyboard mode
        const useNumeric = context.parameters.useNumericKeyboard.raw ?? false;
        this.applyVirtualKeyboardMode(useNumeric);

        // Apply max length if specified
        const maxLength = context.parameters.maxLength.raw;
        if (maxLength && maxLength > 0) {
            this._inputElement.maxLength = maxLength;
        } else {
            this._inputElement.removeAttribute("maxlength");
        }

        // Apply styles
        this.applyStyles(context);
    }

    private applyStyles(context: ComponentFramework.Context<IInputs>): void {
        const style = this._inputElement.style;

        // Basic layout
        style.width = "100%";
        style.boxSizing = "border-box";
        style.padding = "4px";

        // Border styling
        const borderColor = context.parameters.borderColor.raw || "#c8c8c8";
        const borderThickness = context.parameters.borderThickness.raw || 1;
        style.border = `${borderThickness}px solid ${borderColor}`;

        // Height
        const height = context.parameters.height.raw || 32;
        style.height = `${height}px`;

        // Text alignment
        const textAlignCenter = context.parameters.textAlignCenter.raw ?? false;
        const textAlignRight = context.parameters.textAlignRight.raw ?? false;
        style.textAlign = this.getTextAlignment(textAlignCenter, textAlignRight);

        // Font properties
        const fontFamily = context.parameters.fontFamily.raw || "Segoe UI, Arial, sans-serif";
        const fontSize = context.parameters.fontSize.raw || 14;
        const fontBold = context.parameters.fontBold.raw ?? false;

        style.fontFamily = fontFamily;
        style.fontSize = `${fontSize}px`;
        style.fontWeight = fontBold ? "bold" : "normal";

        // Additional styling for better appearance
        style.borderRadius = "4px";
    }

    private getTextAlignment(center: boolean, right: boolean): string {
        if (right) {
            return "right";
        } else if (center) {
            return "center";
        } else {
            return "left";
        }
    }

    private applyVirtualKeyboardMode(useNumeric: boolean): void {
        if (useNumeric) {
            this._inputElement.inputMode = "numeric";
            this._inputElement.pattern = "[0-9]*";
            this._inputElement.type = "text"; // Keep as text but with numeric input mode
        } else {
            this._inputElement.inputMode = "text";
            this._inputElement.type = "text";
            // Remove pattern if it was set
            this._inputElement.removeAttribute("pattern");
        }
    }



    private handleInput(): void {
        this._value = this._inputElement.value;

        if (this._triggerWhileTyping) {
            this._notifyOutputChanged();
        } else if (this._triggerDelayed) {
            const delay = this._context.parameters.delayMs.raw || 500;
            if (this._delayTimer) {
                window.clearTimeout(this._delayTimer);
            }
            this._delayTimer = window.setTimeout(() => {
                this._notifyOutputChanged();
            }, delay);
        }
    }

    private handleBlur(): void {
        if (this._triggerOnFocusOut) {
            this._value = this._inputElement.value;
            this._notifyOutputChanged();
        }
    }

    public updateView(context: ComponentFramework.Context<IInputs>): void {
        this._context = context;
        this._triggerOnFocusOut = context.parameters.triggerOnFocusOut.raw ?? true;
        this._triggerWhileTyping = context.parameters.triggerWhileTyping.raw ?? false;
        this._triggerDelayed = context.parameters.triggerDelayed.raw ?? false;
        this.updateInputElement(context);
    }

    public getOutputs(): IOutputs {
        return {
            text: this._value
        };
    }

    public destroy(): void {
        // Remove event listeners
        this._inputElement.removeEventListener("input", this.handleInput.bind(this));
        this._inputElement.removeEventListener("blur", this.handleBlur.bind(this));
        
        // Clear timer
        if (this._delayTimer) {
            window.clearTimeout(this._delayTimer);
        }
        
        // Remove element
        if (this._inputElement && this._inputElement.parentNode) {
            this._inputElement.parentNode.removeChild(this._inputElement);
        }
    }
}