import { IInputs, IOutputs } from "./generated/ManifestTypes";

export class TextInputV3 implements ComponentFramework.StandardControl<IInputs, IOutputs> {
    private _container: HTMLDivElement;
    private _inputElement: HTMLInputElement;
    private _notifyOutputChanged: () => void;
    private _value: string = "";
    private _triggerOnFocusOut: boolean = true;
    private _triggerWhileTyping: boolean = false;
    private _triggerDelayed: boolean = false;
    private _userHasModified: boolean = false;
    private _lastUserValue: string = "";
    private _initialBoundValue: string = "";
    private _isInGallery: boolean = false;
    private _rowIdentifier: string = "";
    private _hasUserInteracted: boolean = false;
    private _currentRowValue: string = "";
    private _preventExternalUpdates: boolean = false;
    private _isUserTyping: boolean = false;
    private _delayTimer: number | undefined;
    private _context: ComponentFramework.Context<IInputs>;
    private _isInitialized: boolean = false;
    private _stableRowId: string = "";

    // Static storage for gallery values across all instances
    private static _galleryValues: Map<string, string> = new Map();
    // Track initialization order to prevent collisions
    private static _initializationCounter: number = 0;
    private static _pendingInitializations: Set<string> = new Set();

    constructor() { }

    public init(
        context: ComponentFramework.Context<IInputs>,
        notifyOutputChanged: () => void,
        state: ComponentFramework.Dictionary,
        container: HTMLDivElement
    ): void {
        this._context = context;
        this._container = container;
        this._notifyOutputChanged = notifyOutputChanged;
        this._triggerOnFocusOut = context.parameters.triggerOnFocusOut.raw ?? true;
        this._triggerWhileTyping = context.parameters.triggerWhileTyping.raw ?? false;
        this._triggerDelayed = context.parameters.triggerDelayed.raw ?? false;

        // Capture initial bound value
        this._initialBoundValue = context.parameters.text.raw || "";

        // Create input element immediately
        this._inputElement = document.createElement("input");
        this._inputElement.classList.add("text-input-v3");

        // Set initial value immediately
        this._inputElement.value = this._initialBoundValue;

        // Apply basic properties
        this.applyInputProperties(context);

        // Add event listeners
        this._inputElement.addEventListener("input", this.handleInput.bind(this));
        this._inputElement.addEventListener("blur", this.handleBlur.bind(this));
        this._inputElement.addEventListener("focus", this.handleFocus.bind(this));

        // Add to container
        container.appendChild(this._inputElement);

        // Generate row ID with collision detection - use multiple strategies
        this.initializeRowIdentification(context);
    }

    private initializeRowIdentification(context: ComponentFramework.Context<IInputs>): void {
        // Increment counter for each initialization
        const initOrder = ++TextInputV3._initializationCounter;
        
        // Try multiple identification strategies in order of preference
        const identificationStrategies = [
            () => this.getEntityBasedId(context),
            () => this.getContentBasedId(context),
            () => this.getDOMBasedId(),
            () => this.getPositionBasedId(),
            () => this.getFallbackId(initOrder)
        ];

        let finalId: string | null = null;
        
        // Try each strategy until we get a unique ID
        for (const strategy of identificationStrategies) {
            const candidateId = strategy();
            if (candidateId && !TextInputV3._pendingInitializations.has(candidateId) && !TextInputV3._galleryValues.has(candidateId)) {
                finalId = candidateId;
                break;
            }
        }

        // If all strategies failed, create a guaranteed unique ID
        if (!finalId) {
            finalId = `unique_${initOrder}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        this._stableRowId = finalId;
        this._rowIdentifier = finalId;
        
        // Mark this ID as being initialized
        TextInputV3._pendingInitializations.add(finalId);
        
        console.log(`Row initialized with unique ID: ${finalId} (order: ${initOrder}, bound: "${this._initialBoundValue}")`);

        // Enhanced gallery detection
        this._isInGallery = this.detectGalleryContext(context);

        // Set up delayed finalization to ensure DOM is ready
        setTimeout(() => {
            this.finalizeInitialization(context);
        }, 10);
    }

    private finalizeInitialization(context: ComponentFramework.Context<IInputs>): void {
        // Remove from pending set
        TextInputV3._pendingInitializations.delete(this._stableRowId);
        
        // Now properly initialize the input with stored values
        this.updateInputElement(context);
        this._isInitialized = true;
        
        console.log(`Row ${this._stableRowId} finalized with value: "${this._inputElement.value}"`);
    }

    private getEntityBasedId(context: ComponentFramework.Context<IInputs>): string | null {
        try {
            if ((context as any).page?.entityId) {
                return `entity_${(context as any).page.entityId}`;
            }
            
            const recordContext = (context as any).page?.getClientUrl?.() || 
                                 (context as any).parameters?.recordId?.raw ||
                                 (context as any).mode?.contextInfo?.entityId;
            
            if (recordContext) {
                return `record_${recordContext}`;
            }
        } catch (error) {
            console.warn("Entity-based ID failed:", error);
        }
        return null;
    }

    private getContentBasedId(context: ComponentFramework.Context<IInputs>): string | null {
        const boundValue = context.parameters.text.raw || "";
        const defaultValue = context.parameters.defaultValue.raw || "";
        
        // Only use content-based ID if we have unique content
        if (boundValue && boundValue.trim().length > 0) {
            const contentHash = this.hashString(`content_${boundValue}_${defaultValue}`);
            return contentHash;
        }
        
        return null;
    }

    private getDOMBasedId(): string | null {
        try {
            const containerInfo = this.getContainerIdentifier();
            if (containerInfo) {
                return `container_${this.hashString(containerInfo)}`;
            }
        } catch (error) {
            console.warn("DOM-based ID failed:", error);
        }
        return null;
    }

    private getPositionBasedId(): string | null {
        try {
            const position = this.getContainerPosition();
            if (position && position !== 'pos_0_0') { // Avoid default position
                return `pos_${position}`;
            }
        } catch (error) {
            console.warn("Position-based ID failed:", error);
        }
        return null;
    }

    private getFallbackId(initOrder: number): string {
        return `fallback_${initOrder}_${Date.now()}`;
    }
    
    private getContainerIdentifier(): string | null {
        try {
            // Try to find unique identifiers from the container hierarchy
            let element = this._container;
            const identifiers: string[] = [];
            
            // Walk up the DOM tree looking for unique identifiers
            for (let i = 0; i < 10 && element; i++) {
                if (element.id) {
                    identifiers.push(`id:${element.id}`);
                }
                
                if (element.dataset) {
                    Object.keys(element.dataset).forEach(key => {
                        if (element.dataset[key]) {
                            identifiers.push(`data-${key}:${element.dataset[key]}`);
                        }
                    });
                }
                
                // Look for gallery-specific attributes
                const classList = Array.from(element.classList || []);
                const galleryClasses = classList.filter(cls => 
                    cls.includes('gallery') || 
                    cls.includes('item') || 
                    cls.includes('row') ||
                    cls.includes('container')
                );
                
                if (galleryClasses.length > 0) {
                    identifiers.push(`class:${galleryClasses.join(',')}`);
                }
                
                element = element.parentElement;
            }
            
            return identifiers.length > 0 ? identifiers.join('|') : null;
        } catch (error) {
            console.warn("Container identification failed:", error);
            return null;
        }
    }
    
    private getContainerPosition(): string {
        try {
            // Try to determine position based on DOM structure
            const rect = this._container.getBoundingClientRect();
            return `${Math.round(rect.top)}_${Math.round(rect.left)}`;
        } catch (error) {
            return `pos_${Math.random().toString(36).substr(2, 9)}`;
        }
    }

    private hashString(str: string): string {
        let hash = 0;
        if (str.length === 0) return hash.toString();
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return `row_${Math.abs(hash)}`;
    }

    private detectGalleryContext(context: ComponentFramework.Context<IInputs>): boolean {
        // Enhanced gallery detection
        try {
            // Check if we're in a gallery by examining the container hierarchy
            let parent = this._container.parentElement;
            let depth = 0;
            while (parent && depth < 10) {
                const classList = parent.className || "";
                const id = parent.id || "";
                
                // Look for gallery-specific class names or attributes
                if (classList.includes("gallery") || 
                    classList.includes("Gallery") ||
                    classList.includes("item-container") ||
                    id.includes("gallery")) {
                    return true;
                }
                parent = parent.parentElement;
                depth++;
            }

            // Additional heuristic: if we have a bound value that looks like it could be from a collection
            const boundValue = context.parameters.text.raw;
            if (boundValue && typeof boundValue === 'string') {
                // If the bound value contains patterns that suggest it's from a collection item
                return false; // We'll rely on the update behavior to detect this
            }

            return false;
        } catch (error) {
            console.warn("Gallery detection failed:", error);
            return false;
        }
    }

    private updateInputElement(context: ComponentFramework.Context<IInputs>): void {
        if (!this._isInitialized || !this._stableRowId) {
            // Not fully initialized yet
            return;
        }
        
        const boundValue = context.parameters.text.raw || "";
        const defaultValue = context.parameters.defaultValue.raw || "";

        console.log(`UpdateInputElement for row ${this._stableRowId}: bound="${boundValue}", stored="${TextInputV3._galleryValues.get(this._stableRowId)}"`);

        // Check if we have a stored value for this specific row
        const storedValue = TextInputV3._galleryValues.get(this._stableRowId);
        
        if (storedValue !== undefined) {
            // We have a stored value for this row - use it
            console.log(`Row ${this._rowIdentifier}: Using stored value "${storedValue}"`);
            this._inputElement.value = storedValue;
            this._value = storedValue;
            this._lastUserValue = storedValue;
            this._currentRowValue = storedValue;
            this._userHasModified = true;
            this._hasUserInteracted = true;
            this._preventExternalUpdates = true;
        } else if (!this._userHasModified) {
            // No stored value and user hasn't modified - use bound/default value
            const initialValue = boundValue || defaultValue || "";
            console.log(`Row ${this._rowIdentifier}: Using initial value "${initialValue}"`);
            this._inputElement.value = initialValue;
            this._value = initialValue;
            this._currentRowValue = initialValue;
            
            // Store the initial value so we don't lose it
            if (initialValue) {
                TextInputV3._galleryValues.set(this._stableRowId, initialValue);
            }
        } else {
            // User has modified but no stored value - keep current value
            console.log(`Row ${this._rowIdentifier}: Keeping current value "${this._inputElement.value}"`);
            this._value = this._inputElement.value;
            this._lastUserValue = this._inputElement.value;
        }

        // Apply standard input properties
        this.applyInputProperties(context);
    }

    private applyInputProperties(context: ComponentFramework.Context<IInputs>): void {
        // Basic input setup
        this._inputElement.type = "text";
        this._inputElement.disabled = context.mode.isControlDisabled;

        // Set hint text (placeholder)
        this._inputElement.placeholder = context.parameters.hintText.raw || "";

        // Virtual keyboard mode
        const useNumeric = context.parameters.useNumericKeyboard.raw ?? false;
        this.applyVirtualKeyboardMode(useNumeric);

        // Apply max length if specified
        const maxLength = context.parameters.maxLength.raw;
        if (maxLength && maxLength > 0) {
            this._inputElement.maxLength = maxLength;
        } else {
            this._inputElement.removeAttribute("maxlength");
            this._inputElement.maxLength = 524288;
        }

        // Apply styles
        this.applyStyles(context);
    }

    private applyStyles(context: ComponentFramework.Context<IInputs>): void {
        const style = this._inputElement.style;

        // Basic layout
        style.width = "100%";
        style.boxSizing = "border-box";
        style.padding = "4px";

        // Border styling
        const borderColor = context.parameters.borderColor.raw || "#c8c8c8";
        const borderThickness = context.parameters.borderThickness.raw || 1;
        style.border = `${borderThickness}px solid ${borderColor}`;

        // Height
        const height = context.parameters.height.raw || 32;
        style.height = `${height}px`;

        // Text alignment
        const textAlignCenter = context.parameters.textAlignCenter.raw ?? false;
        const textAlignRight = context.parameters.textAlignRight.raw ?? false;
        style.textAlign = this.getTextAlignment(textAlignCenter, textAlignRight);

        // Font properties
        const fontFamily = context.parameters.fontFamily.raw || "Segoe UI, Arial, sans-serif";
        const fontSize = context.parameters.fontSize.raw || 14;
        const fontBold = context.parameters.fontBold.raw ?? false;

        style.fontFamily = fontFamily;
        style.fontSize = `${fontSize}px`;
        style.fontWeight = fontBold ? "bold" : "normal";

        // Additional styling
        style.borderRadius = "4px";
    }

    private getTextAlignment(center: boolean, right: boolean): string {
        if (right) return "right";
        if (center) return "center";
        return "left";
    }

    private applyVirtualKeyboardMode(useNumeric: boolean): void {
        if (useNumeric) {
            this._inputElement.inputMode = "numeric";
            this._inputElement.setAttribute("pattern", "[0-9]*");
            this._inputElement.type = "text";
        } else {
            this._inputElement.inputMode = "text";
            this._inputElement.type = "text";
            this._inputElement.removeAttribute("pattern");
        }
    }

    private handleFocus(): void {
        console.log(`Row ${this._rowIdentifier}: Input focused - Current value: "${this._inputElement.value}"`);
        
        // Only regenerate ID if we're not fully initialized or if we detect an issue
        if (!this._isInitialized || this._stableRowId.startsWith('fallback_')) {
            const currentValue = this._inputElement.value;
            const newRowId = this.getEntityBasedId(this._context) || 
                           this.getContentBasedId(this._context) || 
                           this.getDOMBasedId() || 
                           this._stableRowId; // Keep current if no better option
            
            if (newRowId !== this._stableRowId && !TextInputV3._galleryValues.has(newRowId)) {
                console.log(`Row ID updated on focus: ${this._stableRowId} -> ${newRowId}`);
                
                // Move the stored value to the new ID
                if (TextInputV3._galleryValues.has(this._stableRowId)) {
                    const storedValue = TextInputV3._galleryValues.get(this._stableRowId);
                    TextInputV3._galleryValues.delete(this._stableRowId);
                    if (storedValue !== undefined) {
                        TextInputV3._galleryValues.set(newRowId, storedValue);
                    }
                }
                
                this._stableRowId = newRowId;
                this._rowIdentifier = newRowId;
            }
        }
        
        // Check if we have a stored value for this row
        const storedValue = TextInputV3._galleryValues.get(this._stableRowId);
        if (storedValue !== undefined && storedValue !== this._inputElement.value) {
            console.log(`Focus: Loading stored value "${storedValue}" for row ${this._stableRowId}`);
            this._inputElement.value = storedValue;
            this._value = storedValue;
            this._lastUserValue = storedValue;
            this._currentRowValue = storedValue;
            this._userHasModified = true;
            this._hasUserInteracted = true;
        }
        
        this._isUserTyping = false; // Reset typing state on focus
    }

    private handleInput(): void {
        this._isUserTyping = true;
        const currentValue = this._inputElement.value;
        
        // If this is the first user input and we have a fallback ID, try to get a better one
        if (!this._hasUserInteracted && this._stableRowId.startsWith('fallback_')) {
            const betterRowId = this.getContentBasedId(this._context) || 
                               this.getDOMBasedId() || 
                               this._stableRowId;
            
            if (betterRowId !== this._stableRowId && !TextInputV3._galleryValues.has(betterRowId)) {
                console.log(`Upgrading row ID on first input: ${this._stableRowId} -> ${betterRowId}`);
                this._stableRowId = betterRowId;
                this._rowIdentifier = betterRowId;
            }
        }
        
        this._value = currentValue;
        this._userHasModified = true;
        this._hasUserInteracted = true;
        this._lastUserValue = currentValue;
        this._currentRowValue = currentValue;

        // Store in gallery values map
        TextInputV3._galleryValues.set(this._stableRowId, currentValue);

        console.log(`Row ${this._rowIdentifier}: User input "${currentValue}" - stored in gallery map`);

        // Clear any existing timer
        if (this._delayTimer) {
            clearTimeout(this._delayTimer);
        }

        // Set timeout to mark end of typing
        this._delayTimer = window.setTimeout(() => {
            this._isUserTyping = false;
            console.log(`Row ${this._rowIdentifier}: User finished typing`);
        }, 300);

        // Trigger based on settings
        if (this._triggerWhileTyping) {
            this._notifyOutputChanged();
        } else if (this._triggerDelayed) {
            const delay = this._context.parameters.delayMs.raw || 500;
            if (this._delayTimer) {
                window.clearTimeout(this._delayTimer);
            }
            this._delayTimer = window.setTimeout(() => {
                this._notifyOutputChanged();
            }, delay);
        }
    }

    private handleBlur(): void {
        this._isUserTyping = false;
        const currentValue = this._inputElement.value;
        
        this._value = currentValue;
        this._lastUserValue = currentValue;
        this._currentRowValue = currentValue;
        this._userHasModified = true;
        this._hasUserInteracted = true;
        this._preventExternalUpdates = true;

        // Store the final value in gallery map
        TextInputV3._galleryValues.set(this._stableRowId, currentValue);

        console.log(`Row ${this._rowIdentifier}: Blur - Final value "${currentValue}" stored`);

        if (this._triggerOnFocusOut) {
            this._notifyOutputChanged();
        }
    }

    public updateView(context: ComponentFramework.Context<IInputs>): void {
        this._context = context;
        this._triggerOnFocusOut = context.parameters.triggerOnFocusOut.raw ?? true;
        this._triggerWhileTyping = context.parameters.triggerWhileTyping.raw ?? false;
        this._triggerDelayed = context.parameters.triggerDelayed.raw ?? false;

        // Enhanced gallery detection on updateView
        const currentBoundValue = context.parameters.text.raw || "";
        if (this._isInitialized && this._userHasModified && currentBoundValue !== this._currentRowValue) {
            this._isInGallery = true;
            console.log(`Row ${this._rowIdentifier}: Gallery detected during updateView`);
        }

        // Only update if user hasn't interacted or if this is the initial load
        if (!this._hasUserInteracted || !this._isInitialized) {
            this.updateInputElement(context);
        } else {
            // User has interacted - preserve their value and just update properties
            console.log(`Row ${this._rowIdentifier}: UpdateView - preserving user value "${this._lastUserValue}"`);
            this.applyInputProperties(context);
            
            // Ensure the input still shows the user's value
            if (this._inputElement.value !== this._lastUserValue) {
                this._inputElement.value = this._lastUserValue;
            }
        }
    }

    public getOutputs(): IOutputs {
        return {
            text: this._value,
            controlVersion: "1.1.8"
        };
    }

    public destroy(): void {
        // Clear timer
        if (this._delayTimer) {
            window.clearTimeout(this._delayTimer);
        }
        
        // Remove element
        if (this._inputElement && this._inputElement.parentNode) {
            this._inputElement.parentNode.removeChild(this._inputElement);
        }

        // Clean up static storage for this row (optional - you might want to keep it)
        // TextInputV3._galleryValues.delete(this._stableRowId);
    }
}