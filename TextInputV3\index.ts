import { IInputs, IOutputs } from "./generated/ManifestTypes";

export class TextInputV3 implements ComponentFramework.StandardControl<IInputs, IOutputs> {
    private _container: HTMLDivElement;
    private _inputElement: HTMLInputElement;
    private _notifyOutputChanged: () => void;
    private _value: string = "";
    private _triggerOnFocusOut: boolean = true;
    private _triggerWhileTyping: boolean = false;
    private _triggerDelayed: boolean = false;
    private _userHasModified: boolean = false;
    private _lastUserValue: string = "";
    private _initialBoundValue: string = "";
    private _isInGallery: boolean = false;
    private _rowIdentifier: string = "";
    private _hasUserInteracted: boolean = false;
    private _currentRowValue: string = "";
    private _preventExternalUpdates: boolean = false;
    private _mutationObserver: MutationObserver | null = null;
    private _delayTimer: number | undefined;
    private _context: ComponentFramework.Context<IInputs>;

    constructor() { }

    public init(
        context: ComponentFramework.Context<IInputs>,
        notifyOutputChanged: () => void,
        state: ComponentFramework.Dictionary,
        container: HTMLDivElement
    ): void {
        this._context = context;
        this._container = container;
        this._notifyOutputChanged = notifyOutputChanged;
        this._triggerOnFocusOut = context.parameters.triggerOnFocusOut.raw ?? true;
        this._triggerWhileTyping = context.parameters.triggerWhileTyping.raw ?? false;
        this._triggerDelayed = context.parameters.triggerDelayed.raw ?? false;

        // Capture initial bound value to detect gallery scenarios
        this._initialBoundValue = context.parameters.text.raw || "";

        // Create a unique identifier for this control instance (helps with gallery scenarios)
        this._rowIdentifier = this.generateRowIdentifier(context);

        // Check if we're in a gallery based on initial conditions
        this._isInGallery = this.detectGalleryContext(context);

        // Create input element
        this._inputElement = document.createElement("input");
        this._inputElement.classList.add("text-input-v3");

        // Override the value setter to prevent external interference
        this.protectInputValue();

        // Initialize element
        this.updateInputElement(context);

        // Add event listeners
        this._inputElement.addEventListener("input", this.handleInput.bind(this));
        this._inputElement.addEventListener("blur", this.handleBlur.bind(this));

        // Add to container
        container.appendChild(this._inputElement);
    }

    private updateInputElement(context: ComponentFramework.Context<IInputs>): void {
        const currentInputValue = this._inputElement ? this._inputElement.value : "";
        const boundValue = context.parameters.text.raw;
        const defaultValue = context.parameters.defaultValue.raw;

        // ABSOLUTE PROTECTION: If user has interacted with this control, NEVER allow external updates
        if (this._hasUserInteracted) {
            console.log(`Row ${this._rowIdentifier}: BLOCKING external update. Preserving user value "${this._lastUserValue}"`);
            this._inputElement.value = this._lastUserValue;
            this._currentRowValue = this._lastUserValue;
            this._preventExternalUpdates = true;
            return; // Completely block any external value changes
        }

        // If we're preventing external updates, maintain the user's value
        if (this._preventExternalUpdates) {
            this._inputElement.value = this._lastUserValue;
            return;
        }

        // Only allow initial value setting if user hasn't interacted yet
        if (!this._userHasModified && !this._hasUserInteracted) {
            this._inputElement.value = boundValue || defaultValue || "";
            this._currentRowValue = this._inputElement.value;
        }


        // Basic input setup
        this._inputElement.type = "text";
        this._inputElement.disabled = context.mode.isControlDisabled;

        // Set hint text (placeholder)
        this._inputElement.placeholder = context.parameters.hintText.raw || "";

        // Virtual keyboard mode
        const useNumeric = context.parameters.useNumericKeyboard.raw ?? false;
        this.applyVirtualKeyboardMode(useNumeric);

        // Apply max length if specified
        const maxLength = context.parameters.maxLength.raw;
        if (maxLength && maxLength > 0) {
            this._inputElement.maxLength = maxLength;
        } else {
            // Explicitly remove maxLength and set to unlimited
            this._inputElement.removeAttribute("maxlength");
            // Set to a very high number to ensure unlimited input
            this._inputElement.maxLength = 524288; // HTML5 default max
        }

        // Apply styles
        this.applyStyles(context);
    }

    private applyStyles(context: ComponentFramework.Context<IInputs>): void {
        const style = this._inputElement.style;

        // Basic layout
        style.width = "100%";
        style.boxSizing = "border-box";
        style.padding = "4px";

        // Border styling
        const borderColor = context.parameters.borderColor.raw || "#c8c8c8";
        const borderThickness = context.parameters.borderThickness.raw || 1;
        style.border = `${borderThickness}px solid ${borderColor}`;

        // Height
        const height = context.parameters.height.raw || 32;
        style.height = `${height}px`;

        // Text alignment
        const textAlignCenter = context.parameters.textAlignCenter.raw ?? false;
        const textAlignRight = context.parameters.textAlignRight.raw ?? false;
        style.textAlign = this.getTextAlignment(textAlignCenter, textAlignRight);

        // Font properties
        const fontFamily = context.parameters.fontFamily.raw || "Segoe UI, Arial, sans-serif";
        const fontSize = context.parameters.fontSize.raw || 14;
        const fontBold = context.parameters.fontBold.raw ?? false;

        style.fontFamily = fontFamily;
        style.fontSize = `${fontSize}px`;
        style.fontWeight = fontBold ? "bold" : "normal";

        // Additional styling for better appearance
        style.borderRadius = "4px";
    }

    private getTextAlignment(center: boolean, right: boolean): string {
        if (right) {
            return "right";
        } else if (center) {
            return "center";
        } else {
            return "left";
        }
    }

    private applyVirtualKeyboardMode(useNumeric: boolean): void {
        if (useNumeric) {
            this._inputElement.inputMode = "numeric";
            // Only set pattern for validation, not restriction
            this._inputElement.setAttribute("pattern", "[0-9]*");
            this._inputElement.type = "text"; // Keep as text but with numeric input mode
        } else {
            this._inputElement.inputMode = "text";
            this._inputElement.type = "text";
            // Remove pattern if it was set
            this._inputElement.removeAttribute("pattern");
        }
    }

    private detectGalleryContext(context: ComponentFramework.Context<IInputs>): boolean {
        // Simple heuristic: if we're in a gallery, the container might have specific characteristics
        // This is a basic detection - in practice, galleries often have rapid updateView calls
        return false; // For now, we'll handle this through the updateView logic
    }

    private generateRowIdentifier(context: ComponentFramework.Context<IInputs>): string {
        // Generate a unique identifier for this control instance
        // This helps distinguish between different gallery rows
        const boundValue = context.parameters.text.raw || "";
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 9);
        return `${boundValue}_${timestamp}_${random}`;
    }

    private protectInputValue(): void {
        const self = this;

        // Store original methods
        const originalDescriptor = Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value');
        const originalSetAttribute = this._inputElement.setAttribute.bind(this._inputElement);

        // Override the value property
        Object.defineProperty(this._inputElement, 'value', {
            get: function() {
                if (self._hasUserInteracted && self._preventExternalUpdates) {
                    console.log(`Row ${self._rowIdentifier}: PROTECTED GET - returning "${self._lastUserValue}"`);
                    return self._lastUserValue;
                }
                return originalDescriptor?.get?.call(this) || '';
            },
            set: function(newValue: string) {
                if (self._hasUserInteracted && self._preventExternalUpdates) {
                    console.log(`Row ${self._rowIdentifier}: BLOCKED SET attempt "${newValue}" - keeping "${self._lastUserValue}"`);
                    originalDescriptor?.set?.call(this, self._lastUserValue);
                    return;
                }
                console.log(`Row ${self._rowIdentifier}: ALLOWING SET "${newValue}"`);
                originalDescriptor?.set?.call(this, newValue);
            },
            configurable: true,
            enumerable: true
        });

        // Override setAttribute to block value attribute changes
        this._inputElement.setAttribute = function(name: string, value: string) {
            if (name === 'value' && self._hasUserInteracted && self._preventExternalUpdates) {
                console.log(`Row ${self._rowIdentifier}: BLOCKED setAttribute value="${value}" - keeping "${self._lastUserValue}"`);
                originalSetAttribute('value', self._lastUserValue);
                return;
            }
            originalSetAttribute(name, value);
        };

        // Set up MutationObserver to watch for any DOM changes
        this._mutationObserver = new MutationObserver((mutations) => {
            if (self._hasUserInteracted && self._preventExternalUpdates) {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                        const currentValue = self._inputElement.getAttribute('value');
                        if (currentValue !== self._lastUserValue) {
                            console.log(`Row ${self._rowIdentifier}: MUTATION DETECTED - correcting value attribute from "${currentValue}" to "${self._lastUserValue}"`);
                            self._inputElement.setAttribute('value', self._lastUserValue);
                        }
                    }
                });
            }
        });

        // Start observing
        this._mutationObserver.observe(this._inputElement, {
            attributes: true,
            attributeFilter: ['value']
        });
    }

    private forceInputValue(value: string): void {
        try {
            // Method 1: Direct DOM property access (bypassing our override)
            const originalDescriptor = Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value');
            originalDescriptor?.set?.call(this._inputElement, value);

            // Method 2: Direct attribute manipulation
            this._inputElement.setAttribute('value', value);

            // Method 3: Force the visual display
            (this._inputElement as any).defaultValue = value;

            // Method 4: Ensure the input shows the correct value visually
            if (this._inputElement.value !== value) {
                // If our override is being bypassed, force it through the DOM
                Object.defineProperty(this._inputElement, 'value', {
                    value: value,
                    writable: false,
                    configurable: true
                });
            }

            console.log(`Row ${this._rowIdentifier}: FORCED value to "${value}" - current display: "${this._inputElement.value}"`);
        } catch (error) {
            console.error(`Row ${this._rowIdentifier}: Error forcing value:`, error);
        }
    }

    private lockInputToUserValue(userValue: string): void {
        // Ultimate lockdown - make the input completely immutable to external changes
        const self = this;

        // Create a completely locked property
        Object.defineProperty(this._inputElement, 'value', {
            get: () => {
                console.log(`Row ${self._rowIdentifier}: LOCKED GET - returning "${userValue}"`);
                return userValue;
            },
            set: (newValue: string) => {
                console.log(`Row ${self._rowIdentifier}: LOCKED SET BLOCKED - attempted "${newValue}", keeping "${userValue}"`);
                // Ignore all set attempts
            },
            configurable: false, // Make it non-configurable so it can't be overridden
            enumerable: true
        });

        // Also lock the visual display
        this._inputElement.setAttribute('value', userValue);
        (this._inputElement as any).defaultValue = userValue;
    }



    private handleInput(): void {
        const currentValue = this._inputElement.value;

        // IMMEDIATELY lock this control to the user's input
        this._value = currentValue;
        this._userHasModified = true;
        this._hasUserInteracted = true;
        this._lastUserValue = currentValue;
        this._currentRowValue = currentValue;
        this._preventExternalUpdates = true;

        console.log(`Row ${this._rowIdentifier}: User typing "${currentValue}" - LOCKING CONTROL`);

        if (this._triggerWhileTyping) {
            this._notifyOutputChanged();
        } else if (this._triggerDelayed) {
            const delay = this._context.parameters.delayMs.raw || 500;
            if (this._delayTimer) {
                window.clearTimeout(this._delayTimer);
            }
            this._delayTimer = window.setTimeout(() => {
                this._notifyOutputChanged();
            }, delay);
        }
    }

    private handleBlur(): void {
        if (this._triggerOnFocusOut) {
            // FINAL LOCK: Capture and absolutely preserve the current input value for THIS specific row
            const currentValue = this._inputElement.value;
            this._value = currentValue;
            this._lastUserValue = currentValue;
            this._currentRowValue = currentValue;
            this._userHasModified = true;
            this._hasUserInteracted = true;
            this._preventExternalUpdates = true;

            console.log(`Row ${this._rowIdentifier}: FINAL LOCK - User entered "${currentValue}"`);

            this._notifyOutputChanged();

            // AGGRESSIVE preservation with multiple timeouts and intervals
            const preserveValue = () => {
                // Force the value using multiple methods
                this.forceInputValue(currentValue);
                this._lastUserValue = currentValue;
                this._currentRowValue = currentValue;
            };

            // Multiple preservation attempts
            setTimeout(preserveValue, 0);
            setTimeout(preserveValue, 1);
            setTimeout(preserveValue, 5);
            setTimeout(preserveValue, 10);
            setTimeout(preserveValue, 25);
            setTimeout(preserveValue, 50);
            setTimeout(preserveValue, 100);
            setTimeout(preserveValue, 200);

            // Set up a continuous monitor for this value
            const monitor = setInterval(() => {
                const currentDisplayValue = this._inputElement.value;
                if (currentDisplayValue !== currentValue) {
                    console.log(`Row ${this._rowIdentifier}: MONITOR CORRECTING value from "${currentDisplayValue}" to "${currentValue}"`);
                    this.forceInputValue(currentValue);
                }
            }, 50); // Check every 50ms for faster correction

            // Stop monitoring after 2 seconds
            setTimeout(() => clearInterval(monitor), 2000);
        }
    }

    public updateView(context: ComponentFramework.Context<IInputs>): void {
        this._context = context;
        this._triggerOnFocusOut = context.parameters.triggerOnFocusOut.raw ?? true;
        this._triggerWhileTyping = context.parameters.triggerWhileTyping.raw ?? false;
        this._triggerDelayed = context.parameters.triggerDelayed.raw ?? false;

        // Detect gallery scenario: if bound value changes but user has modified input, we're likely in a gallery
        const currentBoundValue = context.parameters.text.raw || "";
        if (this._userHasModified && currentBoundValue !== this._initialBoundValue) {
            this._isInGallery = true;
        }

        this.updateInputElement(context);
    }

    public getOutputs(): IOutputs {
        return {
            text: this._value,
            controlVersion: "1.1.5"
        };
    }

    public destroy(): void {
        // Remove event listeners
        this._inputElement.removeEventListener("input", this.handleInput.bind(this));
        this._inputElement.removeEventListener("blur", this.handleBlur.bind(this));
        
        // Clear timer
        if (this._delayTimer) {
            window.clearTimeout(this._delayTimer);
        }
        
        // Remove element
        if (this._inputElement && this._inputElement.parentNode) {
            this._inputElement.parentNode.removeChild(this._inputElement);
        }
    }
}