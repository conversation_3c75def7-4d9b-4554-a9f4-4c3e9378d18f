import { IInputs, IOutputs } from "./generated/ManifestTypes";

export class TextInputV3 implements ComponentFramework.StandardControl<IInputs, IOutputs> {
    private _container: HTMLDivElement;
    private _inputElement: HTMLInputElement;
    private _notifyOutputChanged: () => void;
    private _value: string = "";
    private _triggerOnFocusOut: boolean = true;
    private _triggerWhileTyping: boolean = false;
    private _triggerDelayed: boolean = false;
    private _userHasModified: boolean = false;
    private _lastUserValue: string = "";
    private _initialBoundValue: string = "";
    private _isInGallery: boolean = false;
    private _delayTimer: number | undefined;
    private _context: ComponentFramework.Context<IInputs>;

    constructor() { }

    public init(
        context: ComponentFramework.Context<IInputs>,
        notifyOutputChanged: () => void,
        state: ComponentFramework.Dictionary,
        container: HTMLDivElement
    ): void {
        this._context = context;
        this._container = container;
        this._notifyOutputChanged = notifyOutputChanged;
        this._triggerOnFocusOut = context.parameters.triggerOnFocusOut.raw ?? true;
        this._triggerWhileTyping = context.parameters.triggerWhileTyping.raw ?? false;
        this._triggerDelayed = context.parameters.triggerDelayed.raw ?? false;

        // Capture initial bound value to detect gallery scenarios
        this._initialBoundValue = context.parameters.text.raw || "";

        // Check if we're in a gallery based on initial conditions
        this._isInGallery = this.detectGalleryContext(context);

        // Create input element
        this._inputElement = document.createElement("input");
        this._inputElement.classList.add("text-input-v3");

        // Initialize element
        this.updateInputElement(context);

        // Add event listeners
        this._inputElement.addEventListener("input", this.handleInput.bind(this));
        this._inputElement.addEventListener("blur", this.handleBlur.bind(this));

        // Add to container
        container.appendChild(this._inputElement);
    }

    private updateInputElement(context: ComponentFramework.Context<IInputs>): void {
        const currentInputValue = this._inputElement ? this._inputElement.value : "";
        const boundValue = context.parameters.text.raw;
        const defaultValue = context.parameters.defaultValue.raw;

        // Gallery scenario: be very aggressive about preserving user input
        if (this._isInGallery && this._userHasModified && this._triggerOnFocusOut) {
            // In galleries, always preserve user input regardless of bound value changes
            this._inputElement.value = this._lastUserValue;
            return; // Skip all other value setting logic
        }

        // Standard scenario: user has modified input and we're using triggerOnFocusOut
        if (this._userHasModified && this._triggerOnFocusOut) {
            // Preserve the user's value (including empty string if they cleared it)
            this._inputElement.value = this._lastUserValue;
        }
        // Only update the value if there's no current input value or if the bound value has changed
        else if (!currentInputValue || (boundValue && boundValue !== currentInputValue && !this._userHasModified)) {
            this._inputElement.value = boundValue || defaultValue || "";
            // Reset modification tracking when we set a new bound value
            this._userHasModified = false;
            this._lastUserValue = "";
        }


        // Basic input setup
        this._inputElement.type = "text";
        this._inputElement.disabled = context.mode.isControlDisabled;

        // Set hint text (placeholder)
        this._inputElement.placeholder = context.parameters.hintText.raw || "";

        // Virtual keyboard mode
        const useNumeric = context.parameters.useNumericKeyboard.raw ?? false;
        this.applyVirtualKeyboardMode(useNumeric);

        // Apply max length if specified
        const maxLength = context.parameters.maxLength.raw;
        if (maxLength && maxLength > 0) {
            this._inputElement.maxLength = maxLength;
        } else {
            // Explicitly remove maxLength and set to unlimited
            this._inputElement.removeAttribute("maxlength");
            // Set to a very high number to ensure unlimited input
            this._inputElement.maxLength = 524288; // HTML5 default max
        }

        // Apply styles
        this.applyStyles(context);
    }

    private applyStyles(context: ComponentFramework.Context<IInputs>): void {
        const style = this._inputElement.style;

        // Basic layout
        style.width = "100%";
        style.boxSizing = "border-box";
        style.padding = "4px";

        // Border styling
        const borderColor = context.parameters.borderColor.raw || "#c8c8c8";
        const borderThickness = context.parameters.borderThickness.raw || 1;
        style.border = `${borderThickness}px solid ${borderColor}`;

        // Height
        const height = context.parameters.height.raw || 32;
        style.height = `${height}px`;

        // Text alignment
        const textAlignCenter = context.parameters.textAlignCenter.raw ?? false;
        const textAlignRight = context.parameters.textAlignRight.raw ?? false;
        style.textAlign = this.getTextAlignment(textAlignCenter, textAlignRight);

        // Font properties
        const fontFamily = context.parameters.fontFamily.raw || "Segoe UI, Arial, sans-serif";
        const fontSize = context.parameters.fontSize.raw || 14;
        const fontBold = context.parameters.fontBold.raw ?? false;

        style.fontFamily = fontFamily;
        style.fontSize = `${fontSize}px`;
        style.fontWeight = fontBold ? "bold" : "normal";

        // Additional styling for better appearance
        style.borderRadius = "4px";
    }

    private getTextAlignment(center: boolean, right: boolean): string {
        if (right) {
            return "right";
        } else if (center) {
            return "center";
        } else {
            return "left";
        }
    }

    private applyVirtualKeyboardMode(useNumeric: boolean): void {
        if (useNumeric) {
            this._inputElement.inputMode = "numeric";
            // Only set pattern for validation, not restriction
            this._inputElement.setAttribute("pattern", "[0-9]*");
            this._inputElement.type = "text"; // Keep as text but with numeric input mode
        } else {
            this._inputElement.inputMode = "text";
            this._inputElement.type = "text";
            // Remove pattern if it was set
            this._inputElement.removeAttribute("pattern");
        }
    }

    private detectGalleryContext(context: ComponentFramework.Context<IInputs>): boolean {
        // Simple heuristic: if we're in a gallery, the container might have specific characteristics
        // This is a basic detection - in practice, galleries often have rapid updateView calls
        return false; // For now, we'll handle this through the updateView logic
    }



    private handleInput(): void {
        this._value = this._inputElement.value;

        // Track that user has modified the input
        const currentValue = this._inputElement.value;
        this._userHasModified = true;
        this._lastUserValue = currentValue; // This could be empty string if user cleared it

        if (this._triggerWhileTyping) {
            this._notifyOutputChanged();
        } else if (this._triggerDelayed) {
            const delay = this._context.parameters.delayMs.raw || 500;
            if (this._delayTimer) {
                window.clearTimeout(this._delayTimer);
            }
            this._delayTimer = window.setTimeout(() => {
                this._notifyOutputChanged();
            }, delay);
        }
    }

    private handleBlur(): void {
        if (this._triggerOnFocusOut) {
            // Capture and preserve the current input value
            const currentValue = this._inputElement.value;
            this._value = currentValue;
            
            this._lastUserValue = currentValue;
            this._userHasModified = true; // Always mark as modified so we preserve their choice      

            this._notifyOutputChanged();

            // Use setTimeout to ensure the exact user value is preserved after any framework updates
            setTimeout(() => {
                // Force the input to show exactly what the user had when they left the field
                this._inputElement.value = currentValue;
                this._lastUserValue = currentValue;
            }, 0);
        }
    }

    public updateView(context: ComponentFramework.Context<IInputs>): void {
        this._context = context;
        this._triggerOnFocusOut = context.parameters.triggerOnFocusOut.raw ?? true;
        this._triggerWhileTyping = context.parameters.triggerWhileTyping.raw ?? false;
        this._triggerDelayed = context.parameters.triggerDelayed.raw ?? false;

        // Detect gallery scenario: if bound value changes but user has modified input, we're likely in a gallery
        const currentBoundValue = context.parameters.text.raw || "";
        if (this._userHasModified && currentBoundValue !== this._initialBoundValue) {
            this._isInGallery = true;
        }

        this.updateInputElement(context);
    }

    public getOutputs(): IOutputs {
        return {
            text: this._value
        };
    }

    public destroy(): void {
        // Remove event listeners
        this._inputElement.removeEventListener("input", this.handleInput.bind(this));
        this._inputElement.removeEventListener("blur", this.handleBlur.bind(this));
        
        // Clear timer
        if (this._delayTimer) {
            window.clearTimeout(this._delayTimer);
        }
        
        // Remove element
        if (this._inputElement && this._inputElement.parentNode) {
            this._inputElement.parentNode.removeChild(this._inputElement);
        }
    }
}